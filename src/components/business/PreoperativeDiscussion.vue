<template>
  <div class="preoperative-discussion-container">
    <!-- 左侧患者信息详情面板 -->
    <PatientInfoPanel :patient="patient" />

    <!-- 右侧术前讨论内容区域 -->
    <div class="record-content-area">
      <!-- 加载状态 -->
      <div v-if="loading" class="loading-container" v-loading="loading" element-loading-text="正在加载术前讨论...">
        <div style="height: 200px;"></div>
      </div>

      <!-- 内容区域 -->
      <div v-else>
        <!-- 页面标题、时间信息和按钮行 -->
        <div class="page-header">
          <h2 class="title-text">术前讨论</h2>
          <div class="header-actions">
            <div class="time-info-section">
              <div class="time-item">
                <span class="time-label">创建时间：</span>
                <span class="time-value">{{ recordData.recordDatetime }}</span>
              </div>
              <div class="time-item">
                <span class="time-label">更新时间：</span>
                <span class="time-value">{{ recordData.recordUpdateDatetime }}</span>
              </div>
            </div>
            <div class="form-actions">
              <el-button
                class="delete-btn"
                @click="handleDelete"
                :loading="deleteLoading"
                :disabled="!recordData.recordSn"
              >
                删除
              </el-button>
              <el-button
                type="primary"
                class="save-btn"
                @click="handleSave"
                :loading="saveLoading"
              >
                保存
              </el-button>
            </div>
          </div>
        </div>

        <!-- 术前讨论表单 -->
        <div class="record-form">
          <!-- 讨论日期和参加人员行 -->
          <div class="form-item-row">
            <!-- 讨论日期 -->
            <div class="form-item-half">
              <label class="form-label">讨论日期：</label>
              <el-date-picker
                v-model="formData.discussionDatetime"
                type="date"
                placeholder="选择日期"
                format="YYYY-MM-DD"
                value-format="YYYY-MM-DD"
                size="large"
                class="form-date-picker"
              />
            </div>

            <!-- 参加人员 -->
            <div class="form-item-half">
              <label class="form-label">参加人员：</label>
              <el-input
                v-model="formData.participantsList"
                type="textarea"
                placeholder="请输入内容"
                :rows="2"
                class="form-textarea small"
              />
            </div>
          </div>

          <!-- 术前准备和手术指征行 -->
          <div class="form-item-row">
            <!-- 术前准备 -->
            <div class="form-item-half">
              <label class="form-label">术前准备：</label>
              <el-input
                v-model="formData.preoperationPreparation"
                type="textarea"
                placeholder="请输入内容"
                :rows="2"
                class="form-textarea small"
              />
            </div>

            <!-- 手术指征 -->
            <div class="form-item-half">
              <label class="form-label">手术指征：</label>
              <el-input
                v-model="formData.operationIndication"
                type="textarea"
                placeholder="请输入内容"
                :rows="2"
                class="form-textarea small"
              />
            </div>
          </div>

          <!-- 手术方式和手术体位行 -->
          <div class="form-item-row">
            <!-- 手术方式 -->
            <div class="form-item-half">
              <label class="form-label">手术方式：</label>
              <el-input
                v-model="formData.surgeryMethod"
                type="textarea"
                placeholder="请输入内容"
                :rows="2"
                class="form-textarea small"
              />
            </div>

            <!-- 手术体位 -->
            <div class="form-item-half">
              <label class="form-label">手术体位：</label>
              <el-input
                v-model="formData.surgeryPositions"
                type="textarea"
                placeholder="请输入内容"
                :rows="2"
                class="form-textarea small"
              />
            </div>
          </div>

          <!-- 手术步骤 -->
          <div class="form-item">
            <label class="form-label">手术步骤：</label>
            <el-input
              v-model="formData.operationSetps"
              type="textarea"
              placeholder="请输入内容"
              :rows="3"
              class="form-textarea medium"
            />
          </div>

          <!-- 术中注意事项 -->
          <div class="form-item">
            <label class="form-label">术中注意事项：</label>
            <el-input
              v-model="formData.possibleProblemsAndSolution"
              type="textarea"
              placeholder="请输入内容"
              :rows="3"
              class="form-textarea medium"
            />
          </div>

          <!-- 讨论意见 -->
          <div class="form-item">
            <label class="form-label">讨论意见：</label>
            <el-input
              v-model="formData.discussionOpinion"
              type="textarea"
              placeholder="请输入内容"
              :rows="3"
              class="form-textarea medium"
            />
          </div>

          <!-- 讨论小结 -->
          <div class="form-item">
            <label class="form-label">讨论小结：</label>
            <el-input
              v-model="formData.hostConclusion"
              type="textarea"
              placeholder="请输入内容"
              :rows="3"
              class="form-textarea medium"
            />
          </div>
        </div>

      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted, watch } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { apiServices } from '@/api'
import PatientInfoPanel from './PatientInfoPanel.vue'

// 定义组件props
const props = defineProps({
  patient: {
    type: Object,
    required: true,
    default: () => ({})
  }
})

// 响应式数据
const loading = ref(false)
const saveLoading = ref(false)
const deleteLoading = ref(false)

// 术前讨论记录数据
const recordData = reactive({
  recordDatetime: '',
  recordUpdateDatetime: '',
  recordSn: '',
  visitSn: ''
})

// 表单数据
const formData = reactive({
  discussionDatetime: null, // 日期选择器使用null作为初始值
  participantsList: '',
  preoperationPreparation: '',
  operationIndication: '',
  surgeryMethod: '',
  surgeryPositions: '',
  operationSetps: '',
  possibleProblemsAndSolution: '',
  discussionOpinion: '',
  hostConclusion: ''
})

// 加载术前讨论数据
const loadPreoperativeDiscussion = async () => {
  if (!props.patient?.visitSn) {
    console.warn('缺少visitSn，无法加载术前讨论')
    return
  }

  try {
    loading.value = true
    console.log('正在加载术前讨论，visitSn:', props.patient.visitSn)

    const data = await apiServices.preoperativeDiscussion.getDetail(props.patient.visitSn)

    // 更新记录数据
    Object.assign(recordData, {
      recordDatetime: data.recordDatetime || '',
      recordUpdateDatetime: data.recordUpdateDatetime || '',
      recordSn: data.recordSn || '',
      visitSn: data.visitSn || props.patient.visitSn
    })

    // 更新表单数据
    Object.assign(formData, {
      discussionDatetime: data.discussionDatetime || null,
      participantsList: data.participantsList || '',
      preoperationPreparation: data.preoperationPreparation || '',
      operationIndication: data.operationIndication || '',
      surgeryMethod: data.surgeryMethod || '',
      surgeryPositions: data.surgeryPositions || '',
      operationSetps: data.operationSetps || '',
      possibleProblemsAndSolution: data.possibleProblemsAndSolution || '',
      discussionOpinion: data.discussionOpinion || '',
      hostConclusion: data.hostConclusion || ''
    })

    console.log('术前讨论加载成功:', data)
  } catch (error) {
    console.error('加载术前讨论失败:', error)
    ElMessage.error('加载术前讨论失败: ' + error.message)
  } finally {
    loading.value = false
  }
}

// 保存术前讨论
const handleSave = async () => {
  if (!props.patient?.visitSn) {
    ElMessage.error('缺少患者信息，无法保存')
    return
  }

  try {
    saveLoading.value = true

    // 构建保存数据
    const saveData = {
      visitSn: props.patient.visitSn,
      recordSn: recordData.recordSn,
      ...formData
    }

    console.log('正在保存术前讨论:', saveData)

    const result = await apiServices.preoperativeDiscussion.save(saveData)

    // 更新记录信息
    if (result.recordSn) {
      recordData.recordSn = result.recordSn
    }
    if (result.recordDatetime) {
      recordData.recordDatetime = result.recordDatetime
    }
    if (result.recordUpdateDatetime) {
      recordData.recordUpdateDatetime = result.recordUpdateDatetime
    }

    ElMessage.success('保存成功')
    console.log('术前讨论保存成功:', result)
  } catch (error) {
    console.error('保存术前讨论失败:', error)
    ElMessage.error('保存失败: ' + error.message)
  } finally {
    saveLoading.value = false
  }
}

// 删除术前讨论
const handleDelete = async () => {
  if (!props.patient?.visitSn) {
    ElMessage.error('缺少患者信息，无法删除')
    return
  }

  try {
    await ElMessageBox.confirm(
      '确定要删除这条术前讨论吗？删除后无法恢复。',
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }
    )

    deleteLoading.value = true

    console.log('正在删除术前讨论，visitSn:', props.patient.visitSn)

    await apiServices.preoperativeDiscussion.delete(props.patient.visitSn)

    // 清空表单数据
    Object.assign(formData, {
      discussionDatetime: null,
      participantsList: '',
      preoperationPreparation: '',
      operationIndication: '',
      surgeryMethod: '',
      surgeryPositions: '',
      operationSetps: '',
      possibleProblemsAndSolution: '',
      discussionOpinion: '',
      hostConclusion: ''
    })

    // 清空记录数据
    Object.assign(recordData, {
      recordDatetime: '',
      recordUpdateDatetime: '',
      recordSn: '',
      visitSn: props.patient.visitSn
    })

    ElMessage.success('删除成功')
    console.log('术前讨论删除成功')
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除术前讨论失败:', error)
      ElMessage.error('删除失败: ' + error.message)
    }
  } finally {
    deleteLoading.value = false
  }
}

// 监听患者变化，重新加载数据
watch(() => props.patient?.visitSn, (newVisitSn) => {
  if (newVisitSn) {
    loadPreoperativeDiscussion()
  }
}, { immediate: false })

// 组件挂载时加载数据
onMounted(() => {
  if (props.patient?.visitSn) {
    loadPreoperativeDiscussion()
  }
})
</script>

<style scoped>
/* 整体容器 */
.preoperative-discussion-container {
  display: flex;
  width: 100%;
  height: calc(100vh - 180px); /* 使用视口高度减去导航栏高度，确保一屏显示 */
  gap: 20px;
}

/* 右侧记录内容区域 */
.record-content-area {
  flex: 1;
  height: 100%; /* 使用100%高度，与容器保持一致 */
  background: #FFFFFF;
  border-radius: 0 12px 12px 0;
  padding: 12px; /* 进一步减少内边距 */
  display: flex;
  flex-direction: column;
  overflow: hidden; /* 移除滚动条，强制在一屏内显示 */
}

/* 加载容器 */
.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
  min-height: 400px;
}

/* 页面标题、时间信息和按钮行 */
.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  flex-shrink: 0; /* 防止压缩 */
}

.header-actions {
  display: flex;
  align-items: center;
  gap: 40px;
}

.title-text {
  width: auto; /* 自动宽度 */
  height: 20px;
  font-family: 'PingFang SC', sans-serif;
  font-weight: 500;
  font-size: 18px;
  color: #172B4D;
  line-height: 20px;
  text-align: left;
  font-style: normal;
  text-transform: none;
  margin: 0; /* 移除默认margin */
}

/* 时间信息头部 */
.time-info-section {
  display: flex;
  gap: 40px;
}

.time-item {
  display: flex;
  align-items: center;
  gap: 8px;
}

.time-label {
  width: 80px;
  height: 20px;
  font-family: 'PingFang SC', sans-serif;
  font-weight: 400;
  font-size: 16px;
  color: #6B778C;
  line-height: 20px;
  text-align: left;
}

.time-value {
  font-family: 'PingFang SC', sans-serif;
  font-weight: 400;
  font-size: 16px;
  color: #172B4D;
  line-height: 20px;
}

/* 表单区域 */
.record-form {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 8px; /* 进一步减少表单项之间的间距 */
  min-height: 0; /* 允许内容压缩 */
  overflow-y: auto; /* 如果内容过多，允许表单区域滚动 */
}

.form-item {
  display: flex;
  align-items: flex-start;
  gap: 12px;
}

/* 两列表单行样式 */
.form-item-row {
  display: flex;
  gap: 16px; /* 减少列间距 */
  width: 100%;
}

.form-item-half {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  flex: 1;
  /* 设置最小宽度以确保标签和输入框有足够空间 */
  min-width: calc(130px + 12px + 200px); /* 标签宽度 + 间距 + 最小输入框宽度 */
}

.form-label {
  width: 130px;
  font-family: 'PingFang SC', sans-serif;
  font-weight: 400;
  font-size: 16px;
  color: #172B4D;
  line-height: 20px;
  text-align: right;
  margin-top: 8px;
  flex-shrink: 0;
}

/* 确保两列布局中的标签与单列布局保持一致的对齐 */
.form-item-half .form-label {
  width: 130px; /* 与单列布局保持相同宽度，确保冒号垂直对齐 */
}

/* 响应式布局 - 当空间不足时改为单列 */
@media (max-width: 1200px) {
  .form-item-row {
    flex-direction: column;
    gap: 20px;
  }

  .form-item-half {
    min-width: auto; /* 移除最小宽度限制 */
  }

  .form-item-half .form-label {
    width: 130px; /* 保持标签宽度一致 */
  }
  .time-info-section{
    display: none;
  }
}

/* ======================================================= */
/* ====== 日期选择器 (DatePicker) 样式 ====== */
/* ======================================================= */

/* 日期选择器输入框包装器 - 默认状态 */
:deep(.el-date-editor.form-date-picker .el-input__wrapper) {
  /* 如果视觉上仍然偏淡，可以尝试稍微调深的颜色 */
  border: 1px solid #EBECF0 !important; /* 与文本框完全一致 */
  box-shadow: 0 0 0 1px #e5e7eb inset;
  background-color: #FFFFFF !important;
  border-radius: 4px !important;
  transition: border-color 0.2s ease !important;
  /* 强制重置所有可能影响边框显示的属性 */
  outline: none !important;
  background-image: none !important;
  filter: none !important;
  opacity: 1 !important;
}

/* 鼠标悬浮状态 - 与文本框保持一致，不改变边框颜色 */
:deep(.el-date-editor.form-date-picker:hover .el-input__wrapper) {
  border-color: #DFE1E6 !important;
  box-shadow: 0 0 0 1px #c0c4cc inset;
}

/* 聚焦状态 - 移除默认的淡蓝色边框，使用自定义样式 */
:deep(.el-date-editor.form-date-picker .el-input__wrapper.is-focus),
:deep(.el-date-editor.form-date-picker.is-focus .el-input__wrapper) {
  border-color: #1678FF !important;

}

/* 输入框内部样式 */
:deep(.el-date-editor.form-date-picker .el-input__inner) {
  font-family: 'PingFang SC', sans-serif !important;
  font-size: 14px !important;
  color: #172B4D !important;
  background-color: transparent !important;
  border: none !important;
  box-shadow: none !important;
  height: auto !important;
  line-height: 1.4 !important;
}

/* 占位符样式 */
:deep(.el-date-editor.form-date-picker .el-input__inner::placeholder) {
  color: #C1C7D0 !important;
  font-family: 'PingFang SC', sans-serif !important;
}

/* 文本框样式 */
.form-textarea {
  width: 100%;
}

.form-textarea.small :deep(.el-textarea__inner) {
  height: 48px; /* 减少高度，适配紧凑布局 */
  background: #FFFFFF;
  border-radius: 4px;
  border: 1px solid #EBECF0;
  font-family: 'PingFang SC', sans-serif;
  font-size: 14px;
  color: #172B4D;
  resize: none;
  line-height: 1.3; /* 调整行高 */
}

.form-textarea.medium :deep(.el-textarea__inner) {
  height: 60px; /* 减少高度，适配紧凑布局 */
  background: #FFFFFF;
  border-radius: 4px;
  border: 1px solid #EBECF0;
  font-family: 'PingFang SC', sans-serif;
  font-size: 14px;
  color: #172B4D;
  resize: none;
  line-height: 1.3; /* 调整行高 */
}

.form-textarea.large :deep(.el-textarea__inner) {
  height: 100px; /* 大文本框高度 */
  background: #FFFFFF;
  border-radius: 4px;
  border: 1px solid #EBECF0;
  font-family: 'PingFang SC', sans-serif;
  font-size: 14px;
  color: #172B4D;
  resize: none;
  line-height: 1.4; /* 调整行高 */
}

/* 文本框聚焦状态 */
.form-textarea :deep(.el-textarea__inner:focus) {
  border-color: #1678FF;

}

/* 文本框占位符样式 */
.form-textarea :deep(.el-textarea__inner::placeholder) {
  color: #C1C7D0;
  font-family: 'PingFang SC', sans-serif;
}

/* 底部按钮区域 */
.form-actions {
  display: flex;
  justify-content: flex-end;
  gap: 16px;
  flex-shrink: 0; /* 防止压缩 */
}

.delete-btn {
  width: 80px;
  height: 36px;
  background: #FFFFFF;
  border: 1px solid #FF5630;
  border-radius: 4px;
  font-family: 'PingFang SC', sans-serif;
  font-weight: 400;
  font-size: 14px;
  color: #FF5630;
  line-height: 20px;
}

.delete-btn:hover {
  background: #FFF2F0;
  border-color: #FF5630;
  color: #FF5630;
}

.save-btn {
  width: 80px;
  height: 36px;
  background: #1678FF;
  border: 1px solid #1678FF;
  border-radius: 4px;
  font-family: 'PingFang SC', sans-serif;
  font-weight: 400;
  font-size: 14px;
  color: #FFFFFF;
  line-height: 20px;
}

.save-btn:hover {
  background: #0052CC;
  border-color: #0052CC;
}
</style>
