<template>
  <div class="first-course-record-container">
    <!-- 左侧患者信息详情面板 -->
    <PatientInfoPanel :patient="patient" />

    <!-- 右侧首次病程记录内容区域 -->
    <div class="record-content-area">
      <!-- 加载状态 -->
      <div v-if="loading" class="loading-container" v-loading="loading" element-loading-text="正在加载首次病程记录...">
        <div style="height: 200px;"></div>
      </div>

      <!-- 内容区域 -->
      <div v-else>
        <!-- 页面标题、时间信息和按钮行 -->
        <div class="page-header">
          <h2 class="title-text">首次病程记录</h2>
          <div class="header-actions">
            <div class="time-info-section">
              <div class="time-item">
                <span class="time-label">创建时间：</span>
                <span class="time-value">{{ recordData.recordDatetime }}</span>
              </div>
              <div class="time-item">
                <span class="time-label">更新时间：</span>
                <span class="time-value">{{ recordData.recordUpdateDatetime }}</span>
              </div>
            </div>
            <div class="form-actions">
              <el-button
                class="delete-btn"
                @click="handleDelete"
                :loading="deleteLoading"
                :disabled="!recordData.recordSn"
              >
                删除
              </el-button>
              <el-button
                type="primary"
                class="save-btn"
                @click="handleSave"
                :loading="saveLoading"
              >
                保存
              </el-button>
            </div>
          </div>
        </div>

        <!-- 首次病程记录表单 -->
        <div class="record-form">
          <!-- 入院初步诊断 -->
          <div class="form-item">
            <label class="form-label">入院初步诊断：</label>
            <el-input
              v-model="formData.primaryDiagnosis"
              type="textarea"
              placeholder="请输点击选择诊断信息"
              :rows="2"
              class="form-textarea small diagnosis-clickable"
              @click="handleDiagnosisInputClick"
            />
          </div>

          <!-- 本病历特点 -->
          <div class="form-item">
            <label class="form-label">本病历特点：</label>
            <el-input
              v-model="formData.caseCharacter"
              type="textarea"
              placeholder="请输入内容"
              :rows="3"
              class="form-textarea medium"
            />
          </div>

          <!-- 主诉 -->
          <div class="form-item">
            <label class="form-label">主诉：</label>
            <el-input
              v-model="formData.chiefComplaint"
              type="textarea"
              placeholder="请输入内容"
              :rows="2"
              class="form-textarea small"
            />
          </div>

          <!-- 体格检查和辅助检查 - 同行显示 -->
          <div class="form-item-row">
            <div class="form-item-half">
              <label class="form-label">体格检查：</label>
              <el-input
                v-model="formData.physicalExam"
                type="textarea"
                placeholder="请输入内容"
                :rows="2"
                class="form-textarea small"
              />
            </div>
            <div class="form-item-half">
              <label class="form-label">辅助检查：</label>
              <el-input
                v-model="formData.auxiliaryExam"
                type="textarea"
                placeholder="请输入内容"
                :rows="2"
                class="form-textarea small"
              />
            </div>
          </div>

          <!-- 诊断依据 -->
          <div class="form-item">
            <label class="form-label">诊断依据：</label>
            <el-input
              v-model="formData.diagnosisBasis"
              type="textarea"
              placeholder="请输入内容"
              :rows="2"
              class="form-textarea small"
            />
          </div>

          <!-- 鉴别诊断 -->
          <div class="form-item">
            <label class="form-label">鉴别诊断：</label>
            <el-input
              v-model="formData.differentiatedDiagnosisDesc"
              type="textarea"
              placeholder="请输入内容"
              :rows="2"
              class="form-textarea small"
            />
          </div>

          <!-- 诊疗计划 -->
          <div class="form-item">
            <label class="form-label">诊疗计划：</label>
            <el-input
              v-model="formData.treatmentPlan"
              type="textarea"
              placeholder="请输入内容"
              :rows="2"
              class="form-textarea small"
            />
          </div>
        </div>
      </div>
    </div>

    <!-- 诊断信息选择对话框 -->
    <DiagnosisSelector
      v-model="showDiagnosisSelector"
      :patient="patient"
      :diag-type="'入院诊断'"
      @confirm="handleDiagnosisConfirm"
    />
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted, watch } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { apiServices } from '@/api'
import PatientInfoPanel from './PatientInfoPanel.vue'
import DiagnosisSelector from './DiagnosisSelector.vue'

// 定义组件props
const props = defineProps({
  patient: {
    type: Object,
    required: true,
    default: () => ({})
  }
})

// 定义组件事件
const emit = defineEmits(['patient-data-updated'])

// 响应式数据
const loading = ref(false)
const saveLoading = ref(false)
const deleteLoading = ref(false)
const showDiagnosisSelector = ref(false)

// 首次病程记录数据
const recordData = reactive({
  recordDatetime: '',
  recordUpdateDatetime: '',
  recordSn: '',
  visitSn: ''
})

// 表单数据
const formData = reactive({
  primaryDiagnosis: '',
  caseCharacter: '',
  chiefComplaint: '',
  physicalExam: '',
  auxiliaryExam: '',
  diagnosisBasis: '',
  differentiatedDiagnosisDesc: '',
  treatmentPlan: ''
})




// 加载首次病程记录数据
const loadFirstCourseRecord = async () => {
  if (!props.patient?.visitSn) {
    console.warn('缺少visitSn，无法加载首次病程记录')
    return
  }

  try {
    loading.value = true
    console.log('正在加载首次病程记录，visitSn:', props.patient.visitSn)

    const data = await apiServices.firstCourseRecord.getDetail(props.patient.visitSn)

    // 更新记录数据
    Object.assign(recordData, {
      recordDatetime: data.recordDatetime || '',
      recordUpdateDatetime: data.recordUpdateDatetime || '',
      recordSn: data.recordSn || '',
      visitSn: data.visitSn || props.patient.visitSn
    })

    // 更新表单数据
    Object.assign(formData, {
      primaryDiagnosis: data.primaryDiagnosis || '',
      caseCharacter: data.caseCharacter || '',
      chiefComplaint: data.chiefComplaint || '',
      physicalExam: data.physicalExam || '',
      auxiliaryExam: data.auxiliaryExam || '',
      diagnosisBasis: data.diagnosisBasis || '',
      differentiatedDiagnosisDesc: data.differentiatedDiagnosisDesc || '',
      treatmentPlan: data.treatmentPlan || ''
    })

    console.log('首次病程记录加载成功:', data)
  } catch (error) {
    console.error('加载首次病程记录失败:', error)
    ElMessage.error('加载首次病程记录失败: ' + error.message)
  } finally {
    loading.value = false
  }
}

// 处理保存操作
const handleSave = async () => {
  if (!props.patient?.visitSn) {
    ElMessage.error('缺少患者信息，无法保存')
    return
  }

  try {
    saveLoading.value = true

    // 构建保存数据
    const saveData = {
      visitSn: props.patient.visitSn,
      recordSn: recordData.recordSn,
      ...formData
    }

    console.log('正在保存首次病程记录:', saveData)

    const result = await apiServices.firstCourseRecord.save(saveData)

    // 更新记录信息
    if (result.recordSn) {
      recordData.recordSn = result.recordSn
    }
    if (result.recordDatetime) {
      recordData.recordDatetime = result.recordDatetime
    }
    if (result.recordUpdateDatetime) {
      recordData.recordUpdateDatetime = result.recordUpdateDatetime
    }

    ElMessage.success('保存成功')
    console.log('首次病程记录保存成功:', result)

    // 通知父组件患者数据已更新，需要重新加载
    emit('patient-data-updated')
  } catch (error) {
    console.error('保存首次病程记录失败:', error)
    ElMessage.error('保存失败: ' + error.message)
  } finally {
    saveLoading.value = false
  }
}

// 处理删除操作
const handleDelete = async () => {
  if (!recordData.recordSn) {
    ElMessage.warning('当前没有可删除的记录')
    return
  }

  try {
    await ElMessageBox.confirm(
      '确定要删除这条首次病程记录吗？删除后无法恢复。',
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }
    )

    deleteLoading.value = true

    console.log('正在删除首次病程记录，visitSn:', props.patient.visitSn)

    await apiServices.firstCourseRecord.delete(props.patient.visitSn)

    // 清空表单数据
    Object.assign(formData, {
      primaryDiagnosis: '',
      caseCharacter: '',
      chiefComplaint: '',
      physicalExam: '',
      auxiliaryExam: '',
      diagnosisBasis: '',
      differentiatedDiagnosisDesc: '',
      treatmentPlan: ''
    })

    // 清空记录数据
    Object.assign(recordData, {
      recordDatetime: '',
      recordUpdateDatetime: '',
      recordSn: '',
      visitSn: props.patient.visitSn
    })

    ElMessage.success('删除成功')
    console.log('首次病程记录删除成功')

    // 通知父组件患者数据已更新，需要重新加载
    emit('patient-data-updated')
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除首次病程记录失败:', error)
      ElMessage.error('删除失败: ' + error.message)
    }
  } finally {
    deleteLoading.value = false
  }
}

// ========== 诊断信息选择相关方法 ==========

/**
 * 处理诊断输入框点击
 */
const handleDiagnosisInputClick = () => {
  showDiagnosisSelector.value = true
}

/**
 * 处理诊断信息确认选择
 */
const handleDiagnosisConfirm = (diagnosisText) => {
  console.log('返回的诊断信息:', diagnosisText)

  // 将拼接的诊断信息字符串填入输入框
  if (diagnosisText) {
    formData.primaryDiagnosis = diagnosisText
    ElMessage.success('诊断信息选择成功')
  }
}

// 监听患者变化，重新加载数据
watch(() => props.patient?.visitSn, (newVisitSn) => {
  if (newVisitSn) {
    loadFirstCourseRecord()
  }
}, { immediate: false })

// 组件挂载时加载数据
onMounted(() => {
  if (props.patient?.visitSn) {
    loadFirstCourseRecord()
  }
})
</script>

<style scoped>
/* 整体容器 */
.first-course-record-container {
  display: flex;
  width: 100%;
  height: calc(100vh - 180px); /* 使用视口高度减去导航栏高度，确保一屏显示 */
  gap: 20px;
}



/* 右侧首次病程记录内容区域 */
.record-content-area {
  flex: 1;
  height: 100%; /* 使用100%高度，与容器保持一致 */
  background: #FFFFFF;
  border-radius: 0 12px 12px 0;
  padding: 16px; /* 减少内边距 */
  display: flex;
  flex-direction: column;
  overflow-y: auto; /* 添加垂直滚动，以防内容过多 */
}

/* 加载容器 */
.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
  min-height: 400px;
}

/* 页面标题、时间信息和按钮行 */
.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  flex-shrink: 0; /* 防止压缩 */
}

.header-actions {
  display: flex;
  align-items: center;
  gap: 40px;
}

.title-text {
  width: auto; /* 自动宽度 */
  height: 20px;
  font-family: 'PingFang SC', sans-serif;
  font-weight: 500;
  font-size: 18px;
  color: #172B4D;
  line-height: 20px;
  text-align: left;
  font-style: normal;
  text-transform: none;
  margin: 0; /* 移除默认margin */
}

/* 时间信息头部 */
.time-info-section {
  display: flex;
  gap: 40px;
}

.time-item {
  display: flex;
  align-items: center;
  gap: 8px;
}

.time-label {
  width: 80px;
  height: 20px;
  font-family: 'PingFang SC', sans-serif;
  font-weight: 400;
  font-size: 16px;
  color: #6B778C;
  line-height: 20px;
  text-align: left;
}

.time-value {
  font-family: 'PingFang SC', sans-serif;
  font-weight: 400;
  font-size: 16px;
  color: #172B4D;
  line-height: 20px;
}

/* 表单区域 */
.record-form {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 12px; /* 减少表单项之间的间距 */
  min-height: 0; /* 允许内容压缩 */
}

.form-item {
  display: flex;
  align-items: flex-start;
  gap: 12px;
}

/* 两列表单行样式 */
.form-item-row {
  display: flex;
  gap: 16px; /* 减少列间距 */
  width: 100%;
}

.form-item-half {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  flex: 1;
  /* 设置最小宽度以确保标签和输入框有足够空间 */
  min-width: calc(130px + 12px + 200px); /* 标签宽度 + 间距 + 最小输入框宽度 */
}

.form-label {
  width: 130px;
  font-family: 'PingFang SC', sans-serif;
  font-weight: 400;
  font-size: 16px;
  color: #172B4D;
  line-height: 20px;
  text-align: right;
  margin-top: 8px;
  flex-shrink: 0;
}

/* 确保两列布局中的标签与单列布局保持一致的对齐 */
.form-item-half .form-label {
  width: 130px; /* 与单列布局保持相同宽度，确保冒号垂直对齐 */
}

/* 响应式布局 - 当空间不足时改为单列 */
@media (max-width: 1200px) {
  .form-item-row {
    flex-direction: column;
    gap: 20px;
  }

  .form-item-half {
    min-width: auto; /* 移除最小宽度限制 */
  }

  .form-item-half .form-label {
    width: 130px; /* 保持标签宽度一致 */
  }
  .time-info-section{
    display: none;
  }
}

/* 文本框样式 */
.form-textarea {
  width: 100%;
}

.form-textarea.small :deep(.el-textarea__inner) {
  height: 60px; /* 进一步减少高度，适配2行文本 */
  background: #FFFFFF;
  border-radius: 4px;
  border: 1px solid #EBECF0;
  font-family: 'PingFang SC', sans-serif;
  font-size: 14px;
  color: #172B4D;
  resize: none;
  line-height: 1.4; /* 调整行高 */
}

.form-textarea.medium :deep(.el-textarea__inner) {
  height: 80px; /* 进一步减少高度，适配3行文本 */
  background: #FFFFFF;
  border-radius: 4px;
  border: 1px solid #EBECF0;
  font-family: 'PingFang SC', sans-serif;
  font-size: 14px;
  color: #172B4D;
  resize: none;
  line-height: 1.4; /* 调整行高 */
}

/* 文本框聚焦状态 */
.form-textarea :deep(.el-textarea__inner:focus) {
  border-color: #1678FF;

}

/* 文本框占位符样式 */
.form-textarea :deep(.el-textarea__inner::placeholder) {
  color: #C1C7D0;
  font-family: 'PingFang SC', sans-serif;
}

/* 诊断输入容器 */
.diagnosis-input-container {
  display: flex;
  align-items: flex-start;
  gap: 12px;
}

.diagnosis-input {
  flex: 1;
}

/* 同步按钮样式 */
.sync-btn {
  height: 32px;
  padding: 0 12px;
  background: #1678FF;
  border: 1px solid #1678FF;
  border-radius: 4px;
  font-family: 'PingFang SC', sans-serif;
  font-weight: 400;
  font-size: 12px;
  color: #FFFFFF;
  white-space: nowrap;
  flex-shrink: 0;
  margin-top: 2px; /* 微调对齐 */
}

.sync-btn:hover {
  background: #0052CC;
  border-color: #0052CC;
}

.sync-btn .el-icon {
  margin-right: 4px;
}

/* 底部按钮区域 */
.form-actions {
  display: flex;
  justify-content: flex-end;
  gap: 16px;
  flex-shrink: 0; /* 防止压缩 */
}

.delete-btn {
  width: 80px;
  height: 36px;
  background: #FFFFFF;
  border: 1px solid #FF5630;
  border-radius: 4px;
  font-family: 'PingFang SC', sans-serif;
  font-weight: 400;
  font-size: 14px;
  color: #FF5630;
  line-height: 20px;
}

.delete-btn:hover {
  background: #FFF2F0;
  border-color: #FF5630;
  color: #FF5630;
}

.save-btn {
  width: 80px;
  height: 36px;
  background: #1678FF;
  border: 1px solid #1678FF;
  border-radius: 4px;
  font-family: 'PingFang SC', sans-serif;
  font-weight: 400;
  font-size: 14px;
  color: #FFFFFF;
  line-height: 20px;
}

.save-btn:hover {
  background: #0052CC;
  border-color: #0052CC;
}

/* 入院初步诊断输入框点击样式 */
.diagnosis-clickable :deep(.el-textarea__inner) {
  cursor: pointer;
}

.diagnosis-clickable :deep(.el-textarea__inner:hover) {
  border-color: #1678FF;
}
</style>
