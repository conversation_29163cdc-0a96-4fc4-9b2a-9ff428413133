<template>
  <div class="consultation-record-container">
    <!-- 左侧患者信息详情面板 -->
    <PatientInfoPanel :patient="patient" />

    <!-- 右侧会诊记录内容区域 -->
    <div class="record-content-area">
      <!-- 加载状态 -->
      <div v-if="loading" class="loading-container" v-loading="loading" element-loading-text="正在加载会诊记录...">
        <div style="height: 200px;"></div>
      </div>

      <!-- 内容区域 -->
      <div v-else>
        <!-- 页面标题、时间信息和按钮行 -->
        <div class="page-header">
          <h2 class="title-text">会诊记录</h2>
          <div class="header-actions">
            <div class="time-info-section">
              <div class="time-item">
                <span class="time-label">创建时间：</span>
                <span class="time-value">{{ recordData.recordDatetime }}</span>
              </div>
              <div class="time-item">
                <span class="time-label">更新时间：</span>
                <span class="time-value">{{ recordData.recordUpdateDatetime }}</span>
              </div>
            </div>
            <div class="form-actions">
              <el-button
                class="delete-btn"
                @click="handleDelete"
                :loading="deleteLoading"
                :disabled="!recordData.recordSn"
              >
                删除
              </el-button>
              <el-button
                type="primary"
                class="save-btn"
                @click="handleSave"
                :loading="saveLoading"
              >
                保存
              </el-button>
            </div>
          </div>
        </div>

        <!-- 会诊记录表单 -->
        <div class="record-form">
          <!-- 日期选择行 -->
          <div class="form-item-row">
            <!-- 会诊申请日期 -->
            <div class="form-item-half">
              <label class="form-label">会诊申请日期：</label>
              <el-date-picker
                v-model="formData.consApplyTime"
                type="date"
                size="large"
                placeholder="选择日期"
                format="YYYY-MM-DD"
                value-format="YYYY-MM-DD"
                class="form-date-picker"
              />
            </div>

            <!-- 会诊日期 -->
            <div class="form-item-half">
              <label class="form-label">会诊日期：</label>
              <el-date-picker
                v-model="formData.consTime"
                type="date"
                size="large"
                placeholder="选择日期"
                format="YYYY-MM-DD"
                value-format="YYYY-MM-DD"
                class="form-date-picker"
              />
            </div>
          </div>

          <!-- 诊断名称和邀请科室行 -->
          <div class="form-item-row">
            <!-- 诊断名称 -->
            <div class="form-item-half">
              <label class="form-label">诊断名称：</label>
              <el-select
                v-model="formData.diagnosisConsultationCorrected"
                multiple
                filterable
                allow-create
                default-first-option
                remote
                :remote-method="searchDiagnosisNames"
                placeholder="请选择或输入诊断名称"
                class="form-select"
                :loading="diagnosisLoading"
                collapse-tags
                collapse-tags-tooltip
                :max-collapse-tags="2"
                @change="handleDiagnosisChange"
                @focus="handleDiagnosisNameFocus"
                v-load-more="loadMoreDiagnosisNames"
              >
                <el-option
                  v-for="diagnosis in diagnosisOptions"
                  :key="diagnosis.value"
                  :label="diagnosis.label"
                  :value="diagnosis.label"
                />
                <div v-if="diagnosisNamePagination.loading" class="load-more-indicator">
                  正在加载...
                </div>
              </el-select>
            </div>

            <!-- 邀请科室 -->
            <div class="form-item-half">
              <label class="form-label">邀请科室：</label>
              <el-select
                v-model="formData.deptInvited"
                multiple
                filterable
                placeholder="请选择邀请科室"
                class="form-select"
                :loading="departmentLoading"
                collapse-tags
                collapse-tags-tooltip
                :max-collapse-tags="2"
                @change="handleDepartmentChange"
              >
                <el-option
                  v-for="dept in departmentOptions"
                  :key="dept.id"
                  :label="dept.label"
                  :value="dept.value"
                />
              </el-select>
            </div>
          </div>

          <!-- 辅助检查 -->
          <div class="form-item">
            <label class="form-label">辅助检查：</label>
            <el-input
              v-model="formData.auxiliaryExam"
              type="textarea"
              placeholder="请输入内容"
              :rows="2"
              class="form-textarea small"
            />
          </div>

          <!-- 患者病情 -->
          <div class="form-item">
            <label class="form-label">患者病情：</label>
            <el-input
              v-model="formData.recordAbstract"
              type="textarea"
              placeholder="请输入内容"
              :rows="2"
              class="form-textarea small"
            />
          </div>

          <!-- 诊疗情况 -->
          <div class="form-item">
            <label class="form-label">诊疗情况：</label>
            <el-input
              v-model="formData.treatProDescription"
              type="textarea"
              placeholder="请输入内容"
              :rows="2"
              class="form-textarea small"
            />
          </div>

          <!-- 会诊原因及目的 -->
          <div class="form-item">
            <label class="form-label">会诊原因及目的：</label>
            <el-input
              v-model="formData.consReason"
              type="textarea"
              placeholder="请输入内容"
              :rows="2"
              class="form-textarea small"
            />
          </div>

          <!-- 会诊意见 -->
          <div class="form-item">
            <label class="form-label">会诊意见：</label>
            <el-input
              v-model="formData.consRecommendation"
              type="textarea"
              placeholder="请输入内容"
              :rows="3"
              class="form-textarea medium"
            />
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted, watch } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { apiServices } from '@/api'
import { useDictionaryStore } from '@/stores'
import PatientInfoPanel from './PatientInfoPanel.vue'

// 自定义指令：v-load-more
const vLoadMore = {
  mounted(el, binding) {
    const observer = new MutationObserver(() => {
      const dropdown = document.querySelector('.el-select-dropdown:not([style*="display: none"]) .el-select-dropdown__wrap')
      if (dropdown && !dropdown.hasAttribute('data-scroll-listener')) {
        dropdown.setAttribute('data-scroll-listener', 'true')
        dropdown.addEventListener('scroll', function() {
          const isBottom = this.scrollHeight - this.scrollTop <= this.clientHeight + 1
          if (isBottom) {
            binding.value()
          }
        })
      }
    })

    observer.observe(document.body, {
      childList: true,
      subtree: true
    })

    el.vLoadMoreObserver = observer
  },
  unmounted(el) {
    if (el.vLoadMoreObserver) {
      el.vLoadMoreObserver.disconnect()
    }
  }
}

// 定义组件props
const props = defineProps({
  patient: {
    type: Object,
    required: true,
    default: () => ({})
  }
})

// 使用字典Store
const dictionaryStore = useDictionaryStore()

// 响应式数据
const loading = ref(false)
const saveLoading = ref(false)
const deleteLoading = ref(false)

// 字典数据相关状态
const departmentOptions = ref([])
const diagnosisOptions = ref([])
const departmentLoading = ref(false)
const diagnosisLoading = ref(false)

// 诊断名称分页加载状态
const diagnosisNamePagination = reactive({
  pageNum: 1,
  pageSize: 20,
  total: 0,
  loading: false,
  keyword: ''
})

// 会诊记录数据
const recordData = reactive({
  recordDatetime: '',
  recordUpdateDatetime: '',
  recordSn: '',
  visitSn: ''
})

// 表单数据
const formData = reactive({
  consApplyTime: null, // 日期选择器使用null作为初始值
  consTime: null, // 日期选择器使用null作为初始值
  diagnosisConsultationCorrected: [], // 改为数组，支持多选
  auxiliaryExam: '',
  recordAbstract: '',
  treatProDescription: '',
  consReason: '',
  deptInvited: [], // 改为数组，支持多选
  consRecommendation: ''
})

// ========== 字典数据加载方法 ==========

/**
 * 加载字典数据
 */
const loadDictionaries = async () => {
  try {
    // 并行加载字典数据
    await Promise.all([
      loadDepartments(),
      loadDiagnosis()
    ])
  } catch (error) {
    ElMessage.error('字典数据加载失败，请刷新页面重试')
  }
}

/**
 * 加载邀请科室字典
 */
const loadDepartments = async () => {
  try {
    departmentLoading.value = true
    departmentOptions.value = await dictionaryStore.getInviteDepartments()
  } catch (error) {
    departmentOptions.value = []
  } finally {
    departmentLoading.value = false
  }
}

/**
 * 加载诊断名称字典
 */
const loadDiagnosis = async (isLoadMore = false) => {
  if (diagnosisNamePagination.loading) return

  try {
    diagnosisNamePagination.loading = true

    const params = {
      pageNum: diagnosisNamePagination.pageNum,
      pageSize: diagnosisNamePagination.pageSize,
      label: diagnosisNamePagination.keyword
    }

    const data = await apiServices.dictionary.getDiagnosisNamesByPage(params)

    const newOptions = data.records.map(item => ({
      label: item.label,
      value: item.value
    }))

    if (isLoadMore) {
      // 合并并去重
      const currentValues = new Set(diagnosisOptions.value.map(o => o.value))
      const uniqueNewOptions = newOptions.filter(o => !currentValues.has(o.value))
      diagnosisOptions.value.push(...uniqueNewOptions)
    } else {
      diagnosisOptions.value = newOptions
    }

    diagnosisNamePagination.total = data.total
  } catch (error) {
    if (!isLoadMore) {
      diagnosisOptions.value = []
    }
  } finally {
    diagnosisNamePagination.loading = false
  }
}

/**
 * 加载更多诊断名称
 */
const loadMoreDiagnosisNames = () => {
  const { pageNum, pageSize, total } = diagnosisNamePagination
  if ((pageNum * pageSize < total) && !diagnosisNamePagination.loading) {
    diagnosisNamePagination.pageNum++
    loadDiagnosis(true)
  }
}

/**
 * 搜索诊断名称
 */
const searchDiagnosisNames = async (query) => {
  diagnosisNamePagination.keyword = query
  diagnosisNamePagination.pageNum = 1
  diagnosisLoading.value = true
  try {
    await loadDiagnosis()
  } catch (error) {
    ElMessage.error('搜索诊断名称失败')
  } finally {
    diagnosisLoading.value = false
  }
}

/**
 * 诊断名称选择框聚焦时触发
 */
const handleDiagnosisNameFocus = () => {
  if (diagnosisOptions.value.length === 0) {
    searchDiagnosisNames('')
  }
}

/**
 * 处理邀请科室选择变化
 */
const handleDepartmentChange = (value) => {
}

/**
 * 处理诊断名称选择变化
 */
const handleDiagnosisChange = (value) => {
}

// ========== 会诊记录数据操作方法 ==========

// 加载会诊记录数据
const loadConsultationRecord = async () => {
  if (!props.patient?.visitSn) {
    return
  }

  try {
    loading.value = true

    const data = await apiServices.consultationRecord.getDetail(props.patient.visitSn)

    // 更新记录数据
    Object.assign(recordData, {
      recordDatetime: data.recordDatetime || '',
      recordUpdateDatetime: data.recordUpdateDatetime || '',
      recordSn: data.recordSn || '',
      visitSn: data.visitSn || props.patient.visitSn
    })

    // 更新表单数据（处理字符串到数组的转换）
    Object.assign(formData, {
      consApplyTime: data.consApplyTime || null,
      consTime: data.consTime || null,
      // 将后端返回的字符串转换为数组（逗号分隔）
      diagnosisConsultationCorrected: data.diagnosisConsultationCorrected ?
        data.diagnosisConsultationCorrected.split(',').filter(item => item.trim()) : [],
      auxiliaryExam: data.auxiliaryExam || '',
      recordAbstract: data.recordAbstract || '',
      treatProDescription: data.treatProDescription || '',
      consReason: data.consReason || '',
      // 将后端返回的字符串转换为数组（逗号分隔）
      deptInvited: data.deptInvited ?
        data.deptInvited.split(',').filter(item => item.trim()) : [],
      consRecommendation: data.consRecommendation || ''
    })

  } catch (error) {
    ElMessage.error('加载会诊记录失败: ' + error.message)
  } finally {
    loading.value = false
  }
}

// 保存会诊记录
const handleSave = async () => {
  if (!props.patient?.visitSn) {
    ElMessage.error('缺少患者信息，无法保存')
    return
  }

  try {
    saveLoading.value = true

    // 构建保存数据（处理数组到字符串的转换）
    const saveData = {
      visitSn: props.patient.visitSn,
      recordSn: recordData.recordSn,
      ...formData,
      // 将数组转换为后端需要的逗号分隔字符串
      diagnosisConsultationCorrected: Array.isArray(formData.diagnosisConsultationCorrected) ?
        formData.diagnosisConsultationCorrected.join(',') : formData.diagnosisConsultationCorrected,
      deptInvited: Array.isArray(formData.deptInvited) ?
        formData.deptInvited.join(',') : formData.deptInvited
    }


    const result = await apiServices.consultationRecord.save(saveData)

    // 更新记录信息
    if (result.recordSn) {
      recordData.recordSn = result.recordSn
    }
    if (result.recordDatetime) {
      recordData.recordDatetime = result.recordDatetime
    }
    if (result.recordUpdateDatetime) {
      recordData.recordUpdateDatetime = result.recordUpdateDatetime
    }

    ElMessage.success('保存成功')
  } catch (error) {
    ElMessage.error('保存失败: ' + error.message)
  } finally {
    saveLoading.value = false
  }
}

// 删除会诊记录
const handleDelete = async () => {
  if (!props.patient?.visitSn) {
    ElMessage.error('缺少患者信息，无法删除')
    return
  }

  try {
    await ElMessageBox.confirm(
      '确定要删除这条会诊记录吗？删除后无法恢复。',
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }
    )

    deleteLoading.value = true


    await apiServices.consultationRecord.delete(props.patient.visitSn)

    // 清空表单数据
    Object.assign(formData, {
      consApplyTime: null,
      consTime: null,
      diagnosisConsultationCorrected: [], // 重置为空数组
      auxiliaryExam: '',
      recordAbstract: '',
      treatProDescription: '',
      consReason: '',
      deptInvited: [], // 重置为空数组
      consRecommendation: ''
    })

    // 清空记录数据
    Object.assign(recordData, {
      recordDatetime: '',
      recordUpdateDatetime: '',
      recordSn: '',
      visitSn: props.patient.visitSn
    })

    ElMessage.success('删除成功')
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('删除失败: ' + error.message)
    }
  } finally {
    deleteLoading.value = false
  }
}

// 监听患者变化，重新加载数据
watch(() => props.patient?.visitSn, (newVisitSn) => {
  if (newVisitSn) {
    loadConsultationRecord()
  }
}, { immediate: false })

// 组件挂载时加载数据
onMounted(async () => {
  // 先加载字典数据
  await loadDictionaries()

  // 再加载会诊记录数据
  if (props.patient?.visitSn) {
    loadConsultationRecord()
  }
})
</script>
<style scoped>
/* 整体容器 */
.consultation-record-container {
  display: flex;
  width: 100%;
  height: calc(100vh - 180px); /* 使用视口高度减去导航栏高度，确保一屏显示 */
  gap: 20px;
}

/* 右侧记录内容区域 */
.record-content-area {
  flex: 1;
  height: 100%; /* 使用100%高度，与容器保持一致 */
  background: #FFFFFF;
  border-radius: 0 12px 12px 0;
  padding: 16px; /* 减少内边距 */
  display: flex;
  flex-direction: column;
  overflow-y: auto; /* 添加垂直滚动，以防内容过多 */
}

/* 加载容器 */
.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
  min-height: 400px;
}

/* 页面标题、时间信息和按钮行 */
.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  flex-shrink: 0; /* 防止压缩 */
}

.header-actions {
  display: flex;
  align-items: center;
  gap: 40px;
}

.title-text {
  width: auto; /* 自动宽度 */
  height: 20px;
  font-family: 'PingFang SC', sans-serif;
  font-weight: 500;
  font-size: 18px;
  color: #172B4D;
  line-height: 20px;
  text-align: left;
  font-style: normal;
  text-transform: none;
  margin: 0; /* 移除默认margin */
}

/* 时间信息头部 */
.time-info-section {
  display: flex;
  gap: 40px;
}

.time-item {
  display: flex;
  align-items: center;
  gap: 8px;
}

.time-label {
  width: 80px;
  height: 20px;
  font-family: 'PingFang SC', sans-serif;
  font-weight: 400;
  font-size: 16px;
  color: #6B778C;
  line-height: 20px;
  text-align: left;
}

.time-value {
  font-family: 'PingFang SC', sans-serif;
  font-weight: 400;
  font-size: 16px;
  color: #172B4D;
  line-height: 20px;
}

/* 表单区域 */
.record-form {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 12px; /* 减少表单项之间的间距 */
  min-height: 0; /* 允许内容压缩 */
}

.form-item {
  display: flex;
  align-items: flex-start;
  gap: 12px;
}

/* 两列表单行样式 */
.form-item-row {
  display: flex;
  gap: 16px; /* 减少列间距 */
  width: 100%;
}

.form-item-half {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  flex: 1;
  /* 设置最小宽度以确保标签和输入框有足够空间 */
  min-width: calc(130px + 12px + 200px); /* 标签宽度 + 间距 + 最小输入框宽度 */
}

.form-label {
  width: 130px;
  font-family: 'PingFang SC', sans-serif;
  font-weight: 400;
  font-size: 16px;
  color: #172B4D;
  line-height: 20px;
  text-align: right;
  margin-top: 8px;
  flex-shrink: 0;
}

/* 确保两列布局中的标签与单列布局保持一致的对齐 */
.form-item-half .form-label {
  width: 130px; /* 与单列布局保持相同宽度，确保冒号垂直对齐 */
}

/* 响应式布局 - 当空间不足时改为单列 */
@media (max-width: 1200px) {
  .form-item-row {
    flex-direction: column;
    gap: 20px;
  }

  .form-item-half {
    min-width: auto; /* 移除最小宽度限制 */
  }

  .form-item-half .form-label {
    width: 130px; /* 保持标签宽度一致 */
  }
  .time-info-section{
    display: none;
  }
}

/* ======================================================= */
/* ====== 日期选择器 (DatePicker) 样式 ====== */
/* ======================================================= */

/* 日期选择器容器样式 */


/* 日期选择器输入框包装器 - 默认状态 */
:deep(.el-date-editor.form-date-picker .el-input__wrapper) {
  /* 如果视觉上仍然偏淡，可以尝试稍微调深的颜色 */
  border: 1px solid #EBECF0 !important; /* 与文本框完全一致 */
  box-shadow: 0 0 0 1px #e5e7eb inset;
  background-color: #FFFFFF !important;
  border-radius: 4px !important;
  transition: border-color 0.2s ease !important;
  /* 强制重置所有可能影响边框显示的属性 */
  outline: none !important;
  background-image: none !important;
  filter: none !important;
  opacity: 1 !important;
}

/* 鼠标悬浮状态 - 与文本框保持一致，不改变边框颜色 */
:deep(.el-date-editor.form-date-picker:hover .el-input__wrapper) {
  border-color: #DFE1E6 !important;
  box-shadow: 0 0 0 1px #c0c4cc inset;
}

/* 聚焦状态 - 移除默认的淡蓝色边框，使用自定义样式 */
:deep(.el-date-editor.form-date-picker .el-input__wrapper.is-focus),
:deep(.el-date-editor.form-date-picker.is-focus .el-input__wrapper) {
  border-color: #1678FF !important;
}

/* 输入框内部样式 */
:deep(.el-date-editor.form-date-picker .el-input__inner) {
  font-family: 'PingFang SC', sans-serif !important;
  font-size: 14px !important;
  color: #172B4D !important;
  background-color: transparent !important;
  border: none !important;
  box-shadow: none !important;
  height: auto !important;
  line-height: 1.4 !important;
}

/* 占位符样式 */
:deep(.el-date-editor.form-date-picker .el-input__inner::placeholder) {
  color: #C1C7D0 !important;
  font-family: 'PingFang SC', sans-serif !important;
}


/* 文本框样式 */
.form-textarea {
  width: 100%;
}

.form-textarea.small :deep(.el-textarea__inner) {
  height: 60px; /* 进一步减少高度，适配2行文本 */
  background: #FFFFFF;
  border-radius: 4px;
  border: 1px solid #EBECF0;
  font-family: 'PingFang SC', sans-serif;
  font-size: 14px;
  color: #172B4D;
  resize: none;
  line-height: 1.4; /* 调整行高 */
}

.form-textarea.medium :deep(.el-textarea__inner) {
  height: 80px; /* 进一步减少高度，适配3行文本 */
  background: #FFFFFF;
  border-radius: 4px;
  border: 1px solid #EBECF0;
  font-family: 'PingFang SC', sans-serif;
  font-size: 14px;
  color: #172B4D;
  resize: none;
  line-height: 1.4; /* 调整行高 */
}

.form-textarea.large :deep(.el-textarea__inner) {
  height: 100px; /* 大文本框高度 */
  background: #FFFFFF;
  border-radius: 4px;
  border: 1px solid #EBECF0;
  font-family: 'PingFang SC', sans-serif;
  font-size: 14px;
  color: #172B4D;
  resize: none;
  line-height: 1.4; /* 调整行高 */
}

/* 文本框聚焦状态 */
.form-textarea :deep(.el-textarea__inner:focus) {
  border-color: #1678FF;

}

/* 文本框占位符样式 */
.form-textarea :deep(.el-textarea__inner::placeholder) {
  color: #C1C7D0;
  font-family: 'PingFang SC', sans-serif;
}

/* ======================================================= */
/* ====== 多选下拉框 (Select) 样式 ====== */
/* ======================================================= */

/* 多选下拉框容器样式 */
.form-select {
  width: 100%;
  /* 移除固定高度，让Element Plus的size="large"控制高度 */
}

/* 多选下拉框输入框包装器 - 默认状态 */
:deep(.form-select .el-select__wrapper) {
  border: 1px solid #EBECF0 !important;
  box-shadow: none !important; /* 移除内阴影，避免高度变化 */
  background-color: #FFFFFF !important;
  border-radius: 4px !important;
  transition: border-color 0.2s ease !important; /* 移除height过渡，只保留border-color过渡 */
  /* 使用与日期选择器相同的高度设置 */
  min-height: 40px; /* Element Plus large size的默认高度 */
  height: auto; /* 自动高度，根据内容调整 */
  padding: 8px 12px; /* 调整内边距以匹配large size */
}

/* 鼠标悬浮状态 */
:deep(.form-select .el-select__wrapper:hover) {
  border-color: #DFE1E6 !important;
  box-shadow: none !important; /* 移除内阴影，保持与默认状态一致 */
}

/* 聚焦状态 - 移除淡蓝色框效果 */
:deep(.form-select .el-select__wrapper.is-focused),
:deep(.form-select .el-select__wrapper:focus),
:deep(.form-select .el-select__wrapper:focus-within),
:deep(.form-select.is-focused .el-select__wrapper),
:deep(.form-select:focus .el-select__wrapper) {
  border-color: #1678FF !important;
  box-shadow: none !important; /* 移除淡蓝色阴影框 */
  outline: none !important; /* 移除outline */
}

/* 聚焦状态下的空选择框 - 保持固定高度 */
:deep(.form-select .el-select__wrapper.is-focused:not(.has-value)),
:deep(.form-select .el-select__wrapper:focus:not(.has-value)),
:deep(.form-select .el-select__wrapper:focus-within:not(.has-value)) {
  height: 40px !important; /* 聚焦时空状态保持固定高度 */
}

/* 聚焦状态下有内容的选择框 - 自动高度 */
:deep(.form-select .el-select__wrapper.is-focused.has-value),
:deep(.form-select .el-select__wrapper:focus.has-value),
:deep(.form-select .el-select__wrapper:focus-within.has-value) {
  height: auto !important; /* 聚焦时有内容状态使用自动高度 */
  min-height: 40px !important; /* 最小高度保持一致 */
}

/* 移除选择框本身的聚焦效果 */
:deep(.form-select:focus),
:deep(.form-select.is-focused) {
  box-shadow: none !important;
  outline: none !important;
}

/* 移除输入框的聚焦效果 */
:deep(.form-select .el-select__input:focus) {
  box-shadow: none !important;
  outline: none !important;
}

/* 确保选择框中所有文本元素的字体大小统一 */
:deep(.form-select .el-select__selection),
:deep(.form-select .el-select__selected-item),
:deep(.form-select .el-select__input-wrapper),
:deep(.form-select .el-select__input-calculator) {
  font-family: 'PingFang SC', sans-serif !important;
  font-size: 14px !important;
}

/* 多选标签区域 */
:deep(.form-select .el-select__tags) {
  max-height: 40px; /* 限制为单行显示，避免换行 */
  overflow-y: hidden; /* 隐藏垂直滚动条 */
  overflow-x: auto; /* 允许水平滚动 */
  flex-wrap: nowrap; /* 禁止换行 */
  align-items: center; /* 垂直居中对齐 */
  gap: 4px; /* 增加标签间距 */
  scrollbar-width: none; /* Firefox 隐藏滚动条 */
  -ms-overflow-style: none; /* IE 隐藏滚动条 */
}

/* 隐藏标签区域的滚动条 */
:deep(.form-select .el-select__tags::-webkit-scrollbar) {
  display: none; /* Chrome, Safari, Edge 隐藏滚动条 */
}

/* 单个标签样式 */
:deep(.form-select .el-tag) {
  margin: 0; /* 移除margin，使用gap控制间距 */
  max-width: 100px; /* 减少单个标签最大宽度，避免过长 */
  min-width: 40px; /* 设置最小宽度 */
  background-color: #F4F5F7;
  border-color: #DFE1E6;
  color: #172B4D;
  font-family: 'PingFang SC', sans-serif !important;
  font-size: 13px !important; /* 稍微减小字体以适应更紧凑的布局 */
  height: 22px; /* 减少标签高度，使其更紧凑 */
  line-height: 20px; /* 调整行高 */
  padding: 0 6px; /* 减少内边距 */
  border-radius: 2px; /* 小圆角 */
  flex-shrink: 0; /* 防止标签被压缩 */
  display: inline-flex; /* 使用flex布局 */
  align-items: center; /* 垂直居中 */
}

/* 标签内容样式 */
:deep(.form-select .el-tag__content) {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  font-family: 'PingFang SC', sans-serif !important;
  font-size: 13px !important; /* 与标签字体大小保持一致 */
  max-width: 90px; /* 限制内容区域宽度，为关闭按钮留出空间 */
  flex: 1; /* 占用剩余空间 */
}

/* 标签关闭按钮 */
:deep(.form-select .el-tag .el-tag__close) {
  color: #6B778C;
  font-size: 12px; /* 减小关闭按钮大小 */
  margin-left: 4px; /* 增加与内容的间距 */
  flex-shrink: 0; /* 防止关闭按钮被压缩 */
}

:deep(.form-select .el-tag .el-tag__close:hover) {
  background-color: #FF5630;
  color: #FFFFFF;
}

/* 输入框样式 */
:deep(.form-select .el-select__input-wrapper) {
  flex: 1;
  min-width: 80px; /* 减少最小宽度，为标签留出更多空间 */
  height: 22px; /* 与标签高度保持一致 */
  align-items: center; /* 垂直居中 */
}

:deep(.form-select .el-select__input) {
  font-family: 'PingFang SC', sans-serif !important;
  font-size: 13px !important; /* 与标签字体大小保持一致 */
  color: #172B4D !important;
  border: none !important;
  background: transparent !important;
  height: 22px !important; /* 与标签高度保持一致 */
  line-height: 22px !important; /* 设置行高 */
  caret-color: transparent !important; /* 确保光标隐藏 */
}

/* 空状态时的样式优化 - 确保所有状态下高度都不变 */
:deep(.form-select .el-select__wrapper:not(.has-value)) {
  height: 40px !important; /* 空状态时固定高度，与日期选择器一致，使用!important确保优先级 */
}

/* 有内容时的样式 */
:deep(.form-select .el-select__wrapper.has-value) {
  height: auto !important; /* 有内容时自动高度 */
  min-height: 40px !important; /* 最小高度与日期选择器一致 */
}

/* 占位符样式 */
:deep(.form-select .el-select__placeholder) {
  color: #C1C7D0 !important;
  font-family: 'PingFang SC', sans-serif !important;
  font-size: 14px !important;
}

/* 下拉箭头 */
:deep(.form-select .el-select__caret) {
  color: #6B778C;
  font-size: 14px;
}

/* 清除按钮 */
:deep(.form-select .el-select__clear) {
  color: #6B778C;
  font-size: 14px;
}

:deep(.form-select .el-select__clear:hover) {
  color: #FF5630;
}

/* 加载状态 */
:deep(.form-select .el-select__loading-circle) {
  color: #1678FF;
}

/* 下拉面板样式 */
:deep(.el-select-dropdown) {
  border: 1px solid #EBECF0;
  border-radius: 4px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  max-height: 300px;
}

/* 下拉选项样式 */
:deep(.el-select-dropdown .el-select-dropdown__item) {
  font-family: 'PingFang SC', sans-serif;
  font-size: 14px;
  color: #172B4D;
  padding: 8px 12px;
}

:deep(.el-select-dropdown .el-select-dropdown__item:hover) {
  background-color: #F4F5F7;
}

:deep(.el-select-dropdown .el-select-dropdown__item.is-selected) {
  background-color: #E3F2FD;
  color: #1678FF;
  font-weight: 500;
}

/* 折叠标签提示框样式 */
:deep(.el-tooltip__popper) {
  max-width: 300px;
}

/* 按钮区域 */
.form-actions {
  display: flex;
  justify-content: flex-end;
  gap: 16px;
  flex-shrink: 0; /* 防止压缩 */
}

.delete-btn {
  width: 80px;
  height: 36px;
  background: #FFFFFF;
  border: 1px solid #FF5630;
  border-radius: 4px;
  font-family: 'PingFang SC', sans-serif;
  font-weight: 400;
  font-size: 14px;
  color: #FF5630;
  line-height: 20px;
}

.delete-btn:hover {
  background: #FFF2F0;
  border-color: #FF5630;
  color: #FF5630;
}

.save-btn {
  width: 80px;
  height: 36px;
  background: #1678FF;
  border: 1px solid #1678FF;
  border-radius: 4px;
  font-family: 'PingFang SC', sans-serif;
  font-weight: 400;
  font-size: 14px;
  color: #FFFFFF;
  line-height: 20px;
}

.save-btn:hover {
  background: #0052CC;
  border-color: #0052CC;
}

.load-more-indicator {
  padding: 8px 12px;
  text-align: center;
  color: #999;
  font-size: 14px;
}
</style>
