<template>
  <div class="lab-report-container">
    <!-- 左侧患者信息详情面板 -->
    <PatientInfoPanel :patient="patient" />

    <!-- 右侧检验报告内容区域 -->
    <div class="lab-report-content-area">
      <!-- 加载状态 -->
      <div v-if="loading" class="loading-container" v-loading="loading" element-loading-text="正在加载检验报告...">
        <div style="height: 200px;"></div>
      </div>

      <!-- 内容区域 -->
      <div v-else class="content-wrapper">
        <!-- 页面标题 -->
        <div class="page-title">
          <h2 class="title-text">检验报告</h2>
        </div>

        <!-- 检验报告表格区域 -->
        <div class="lab-report-tables-container">
          <!-- 左侧主表 -->
          <div class="main-table-container">
            <div class="table-wrapper">
              <table class="main-table">
                <!-- 表头 -->
                <thead>
                  <tr class="table-header">
                    <th class="header-cell">检验大项</th>
                    <th class="header-cell">标本类型</th>
                    <th class="header-cell">报告时间</th>
                  </tr>
                </thead>
                <!-- 表体 -->
                <tbody>
                  <tr 
                    v-for="(item, index) in mainList" 
                    :key="item.labSn || index"
                    class="table-row"
                    :class="{ 'selected': selectedMainIndex === index }"
                    @click="handleMainRowClick(item, index)"
                  >
                    <td class="table-cell">{{ item.labType }}</td>
                    <td class="table-cell">{{ item.specimenTypeName }}</td>
                    <td class="table-cell">{{ item.reportDatetime }}</td>
                  </tr>
                  <!-- 空数据提示 -->
                  <tr v-if="mainList.length === 0" class="empty-row">
                    <td colspan="3" class="empty-cell">暂无检验报告数据</td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>

          <!-- 右侧明细表 -->
          <div class="detail-table-container">
            <div class="table-wrapper">
              <table class="detail-table">
                <!-- 表头 -->
                <thead>
                  <tr class="table-header">
                    <th class="header-cell">检验明细</th>
                    <th class="header-cell">结果</th>
                    <th class="header-cell">参考值</th>
                  </tr>
                </thead>
                <!-- 表体 -->
                <tbody>
                  <tr
                    v-for="(item, index) in detailList"
                    :key="index"
                    class="table-row"
                  >
                    <td class="table-cell">{{ item.itemName }}</td>
                    <td class="table-cell">{{ getResultValue(item) }}</td>
                    <td class="table-cell">{{ getReferenceValue(item) }}</td>
                  </tr>
                  <!-- 空数据提示 -->
                  <tr v-if="detailList.length === 0" class="empty-row">
                    <td colspan="3" class="empty-cell">
                      {{ selectedMainIndex === -1 ? '请选择左侧检验大项查看明细' : '暂无明细数据' }}
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { apiServices } from '@/api'
import PatientInfoPanel from './PatientInfoPanel.vue'

// 定义组件props
const props = defineProps({
  patient: {
    type: Object,
    required: true,
    default: () => ({})
  }
})

// 响应式数据
const loading = ref(false)
const mainList = ref([])
const detailList = ref([])
const selectedMainIndex = ref(-1)

// ========== 数据加载方法 ==========

/**
 * 加载检验报告主表列表
 */
const loadMainList = async () => {
  if (!props.patient?.visitSn) {
    console.warn('缺少visitSn，无法加载检验报告主表列表')
    return
  }

  try {
    loading.value = true
    console.log('正在加载检验报告主表列表，visitSn:', props.patient.visitSn)

    const data = await apiServices.labReport.getMainList(props.patient.visitSn)
    mainList.value = data || []

    console.log('检验报告主表列表加载成功:', mainList.value)

    // 如果有数据，默认选中第一条并加载其明细
    if (mainList.value.length > 0) {
      selectedMainIndex.value = 0
      const firstItem = mainList.value[0]
      if (firstItem.labSn) {
        await loadDetailList(firstItem.labSn)
      }
    } else {
      // 重置选中状态和明细数据
      selectedMainIndex.value = -1
      detailList.value = []
    }
  } catch (error) {
    console.error('加载检验报告主表列表失败:', error)
    ElMessage.error('加载检验报告主表列表失败: ' + error.message)
    mainList.value = []
  } finally {
    loading.value = false
  }
}

/**
 * 加载检验报告明细列表
 */
const loadDetailList = async (labSn) => {
  if (!labSn) {
    console.warn('缺少labSn，无法加载检验报告明细列表')
    return
  }

  try {
    console.log('正在加载检验报告明细列表，labSn:', labSn)

    const data = await apiServices.labReport.getDetailList(labSn)
    detailList.value = data || []

    console.log('检验报告明细列表加载成功:', detailList.value)
  } catch (error) {
    console.error('加载检验报告明细列表失败:', error)
    ElMessage.error('加载检验报告明细列表失败: ' + error.message)
    detailList.value = []
  }
}

// ========== 事件处理方法 ==========

/**
 * 处理主表行点击
 */
const handleMainRowClick = async (item, index) => {
  selectedMainIndex.value = index
  
  if (item.labSn) {
    await loadDetailList(item.labSn)
  } else {
    detailList.value = []
  }
}

// ========== 工具方法 ==========

/**
 * 获取结果值（按优先级：定量 > 定性 > 不确定）
 */
const getResultValue = (item) => {
  if (item.resultQuantitative) {
    return item.resultQuantitative
  }
  if (item.resultQualitative) {
    return item.resultQualitative
  }
  if (item.resultHybrid) {
    return item.resultHybrid
  }
  return ''
}

/**
 * 获取结果值和单位的组合显示
 */
const getResultWithUnit = (item) => {
  const result = getResultValue(item)
  const unit = item.resultUnit || ''

  if (result && unit) {
    return `${result} ${unit}`
  }
  return result || ''
}

/**
 * 获取参考值（按优先级：最大～最小 > 参考值）
 */
const getReferenceValue = (item) => {
  if (item.referenceRangeLow && item.referenceRangeHigh) {
    return `${item.referenceRangeLow}～${item.referenceRangeHigh}`
  }
  if (item.referenceRangeHigh) {
    return item.referenceRangeHigh
  }
  return ''
}

// 监听患者变化，重新加载数据
watch(() => props.patient?.visitSn, (newVisitSn) => {
  if (newVisitSn) {
    loadMainList()
  }
}, { immediate: false })

// 组件挂载时加载数据
onMounted(async () => {
  if (props.patient?.visitSn) {
    await loadMainList()
  }
})
</script>

<style scoped>
/* 整体容器 */
.lab-report-container {
  display: flex;
  width: 100%;
  height: calc(100vh - 180px);
  gap: 20px;
}

/* 右侧检验报告内容区域 */
.lab-report-content-area {
  flex: 1;
  height: 100%;
  background: #FFFFFF;
  border-radius: 0 12px 12px 0;
  padding: 16px;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

/* 加载容器 */
.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
  min-height: 400px;
}

/* 内容包装器 */
.content-wrapper {
  display: flex;
  flex-direction: column;
  height: 100%;
  overflow: hidden;
}

/* 页面标题 */
.page-title {
  margin-bottom: 20px;
  flex-shrink: 0;
}

.title-text {
  width: 113px;
  height: 20px;
  font-family: 'PingFang SC', sans-serif;
  font-weight: 500;
  font-size: 18px;
  color: #172B4D;
  line-height: 20px;
  text-align: left;
  font-style: normal;
  text-transform: none;
  margin: 0;
}

/* 检验报告表格容器 */
.lab-report-tables-container {
  flex: 1;
  display: flex;
  gap: 20px;
  overflow: hidden;
}

/* 主表容器 */
.main-table-container {
  width: 50%;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

/* 明细表容器 */
.detail-table-container {
  width: 50%;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}



/* 表格包装器 */
.table-wrapper {
  flex: 1;
  overflow-x: auto;
  overflow-y: auto;
  border: 1px solid #EBECF0;
  border-radius: 4px;
  min-width: 350px;
}

/* 表格样式 */
.main-table,
.detail-table {
  width: 100%;
  border-collapse: collapse;
  table-layout: fixed;
}

/* 表头样式 */
.table-header {
  background: #F8F9FA;
  position: sticky;
  top: 0;
  z-index: 100;
  height: 44px;
}

.header-cell {
  padding: 12px;
  text-align: left;
  font-family: 'PingFang SC', sans-serif;
  font-weight: 500;
  font-size: 14px;
  color: #172B4D;
  line-height: 20px;
  border: 1px solid #EBECF0;
  background: #F8F9FA;
  height: 44px;
  box-sizing: border-box;
}

/* 主表列宽 */
.main-table .header-cell:nth-child(1) { width: 35%; } /* 检验大项 */
.main-table .header-cell:nth-child(2) { width: 30%; } /* 标本类型 */
.main-table .header-cell:nth-child(3) { width: 35%; } /* 报告时间 */

/* 明细表列宽 */
.detail-table .header-cell:nth-child(1) { width: 40%; } /* 检验项目 */
.detail-table .header-cell:nth-child(2) { width: 30%; } /* 结果（含单位） */
.detail-table .header-cell:nth-child(3) { width: 30%; } /* 参考值 */

/* 表格行样式 */
.table-row {
  height: 44px;
  background: #FFFFFF;
  border-radius: 0px 0px 0px 0px;
  cursor: pointer;
}

.table-row:hover {
  background: #F4F5F7;
}

.table-row.selected {
  background: #E6F1FF;
}

.table-row.selected:hover {
  background: #E6F1FF;
}

/* 表格单元格样式 */
.table-cell {
  padding: 12px;
  text-align: left;
  font-family: 'PingFang SC', sans-serif;
  font-weight: 400;
  font-size: 14px;
  color: #172B4D;
  line-height: 20px;
  border: 1px solid #EBECF0;
  vertical-align: middle;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* 空数据行样式 */
.empty-row {
  height: 60px;
  background: #FFFFFF;
}

.empty-cell {
  padding: 20px;
  text-align: center;
  font-family: 'PingFang SC', sans-serif;
  font-weight: 400;
  font-size: 14px;
  color: #8993A4;
  line-height: 20px;
  border: 1px solid #EBECF0;
}
</style>
