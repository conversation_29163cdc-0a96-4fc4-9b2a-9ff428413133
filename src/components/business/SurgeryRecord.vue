<template>
  <div class="surgery-record-container">
    <!-- 左侧患者信息详情面板 -->
    <PatientInfoPanel :patient="patient" />

    <!-- 右侧手术记录内容区域 -->
    <div class="record-content-area">
      <!-- 加载状态 -->
      <div v-if="loading" class="loading-container" v-loading="loading" element-loading-text="正在加载手术记录...">
        <div style="height: 200px;"></div>
      </div>

      <!-- 内容区域 -->
      <div v-else>
        <!-- 页面标题、时间信息和按钮行 -->
        <div class="page-header">
          <h2 class="title-text">手术记录</h2>
          <div class="header-actions">
            <div class="time-info-section">
              <div class="time-item">
                <span class="time-label">创建时间：</span>
                <span class="time-value">{{ recordData.recordDatetime }}</span>
              </div>
              <div class="time-item">
                <span class="time-label">更新时间：</span>
                <span class="time-value">{{ recordData.recordUpdateDatetime }}</span>
              </div>
            </div>
            <div class="form-actions">
              <el-button
                class="delete-btn"
                @click="handleDelete"
                :loading="deleteLoading"
                :disabled="!recordData.recordSn"
              >
                删除
              </el-button>
              <el-button
                type="primary"
                class="save-btn"
                @click="handleSave"
                :loading="saveLoading"
              >
                保存
              </el-button>
            </div>
          </div>
        </div>


        <!-- 手术记录表单 -->
        <div class="record-form">
          <!-- 手术时间行 -->
          <div class="form-item-row">
            <!-- 手术开始时间 -->
            <div class="form-item-half">
              <label class="form-label">手术开始时间：</label>
              <el-date-picker
                v-model="formData.surgeryStarttime"
                type="datetime"
                placeholder="选择日期时间"
                format="YYYY-MM-DD HH:mm:ss"
                value-format="YYYY-MM-DD HH:mm:ss"
                size="large"
                class="form-date-picker"
              />
            </div>

            <!-- 手术结束时间 -->
            <div class="form-item-half">
              <label class="form-label">手术结束时间：</label>
              <el-date-picker
                v-model="formData.surgeryEndtime"
                type="datetime"
                size="large"
                placeholder="选择日期时间"
                format="YYYY-MM-DD HH:mm:ss"
                value-format="YYYY-MM-DD HH:mm:ss"
                class="form-date-picker"
              />
            </div>
          </div>

          <!-- 诊断行 -->
          <div class="form-item-row">
            <!-- 术前诊断 -->
            <div class="form-item-half">
              <label class="form-label">术前诊断：</label>
              <el-input
                v-model="formData.preOperativeDiagnosis"
                type="textarea"
                placeholder="请输点击选择诊断信息"
                :rows="2"
                class="form-textarea small diagnosis-clickable"
                @click="handlePreOperativeDiagnosisClick"
              />
            </div>

            <!-- 术后诊断 -->
            <div class="form-item-half">
              <label class="form-label">术后诊断：</label>
              <el-input
                v-model="formData.postOperationDiagnosis"
                type="textarea"
                placeholder="请输点击选择诊断信息"
                :rows="2"
                class="form-textarea small diagnosis-clickable"
                @click="handlePostOperationDiagnosisClick"
              />
            </div>
          </div>

          <!-- 手术信息行 -->
          <div class="form-item-row">
            <!-- 手术名称 -->
            <div class="form-item-half">
              <label class="form-label">手术名称：</label>
              <el-input
                v-model="formData.surgeryName"
                type="textarea"
                placeholder="请输入内容"
                :rows="2"
                class="form-textarea small"
              />
            </div>

            <!-- 麻醉方式 -->
            <div class="form-item-half">
              <label class="form-label">麻醉方式：</label>
              <el-input
                v-model="formData.anesthesiaMethod"
                type="textarea"
                placeholder="请输入内容"
                :rows="2"
                class="form-textarea small"
              />
            </div>
          </div>

          <!-- 手术经过 -->
          <div class="form-item">
            <label class="form-label">手术经过：</label>
            <el-input
              ref="surgeryProcessRef"
              v-model="formData.surgeryProcess"
              type="textarea"
              placeholder="请输入内容"
              class="form-textarea autosize-surgery-process"
              @input="handleSurgeryProcessInput"
            />
          </div>
        </div>

      </div>
    </div>

    <!-- 术前诊断信息选择对话框 -->
    <DiagnosisSelector
      v-model="showPreOperativeDiagnosisSelector"
      :patient="patient"
      :diag-type="'术前诊断'"
      @confirm="handlePreOperativeDiagnosisConfirm"
    />

    <!-- 术后诊断信息选择对话框 -->
    <DiagnosisSelector
      v-model="showPostOperationDiagnosisSelector"
      :patient="patient"
      :diag-type="'术后诊断'"
      @confirm="handlePostOperationDiagnosisConfirm"
    />
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted, watch, nextTick } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { apiServices } from '@/api'
import PatientInfoPanel from './PatientInfoPanel.vue'
import DiagnosisSelector from './DiagnosisSelector.vue'

// 定义组件props
const props = defineProps({
  patient: {
    type: Object,
    required: true,
    default: () => ({})
  }
})

// 响应式数据
const loading = ref(false)
const saveLoading = ref(false)
const deleteLoading = ref(false)
const showPreOperativeDiagnosisSelector = ref(false)
const showPostOperationDiagnosisSelector = ref(false)

// 文本框引用
const surgeryProcessRef = ref(null)

// 手术记录数据
const recordData = reactive({
  recordDatetime: '',
  recordUpdateDatetime: '',
  recordSn: '',
  visitSn: ''
})

// 表单数据
const formData = reactive({
  preOperativeDiagnosis: '',
  postOperationDiagnosis: '',
  surgeryName: '',
  anesthesiaMethod: '',
  surgeryStarttime: null, // 日期时间选择器使用null作为初始值
  surgeryEndtime: null, // 日期时间选择器使用null作为初始值
  surgeryProcess: ''
})

// 加载手术记录数据
const loadSurgeryRecord = async () => {
  if (!props.patient?.visitSn) {
    console.warn('缺少visitSn，无法加载手术记录')
    return
  }

  try {
    loading.value = true
    console.log('正在加载手术记录，visitSn:', props.patient.visitSn)

    const data = await apiServices.surgeryRecord.getDetail(props.patient.visitSn)

    // 更新记录数据
    Object.assign(recordData, {
      recordDatetime: data.recordDatetime || '',
      recordUpdateDatetime: data.recordUpdateDatetime || '',
      recordSn: data.recordSn || '',
      visitSn: data.visitSn || props.patient.visitSn
    })

    // 更新表单数据
    Object.assign(formData, {
      preOperativeDiagnosis: data.preOperativeDiagnosis || '',
      postOperationDiagnosis: data.postOperationDiagnosis || '',
      surgeryName: data.surgeryName || '',
      anesthesiaMethod: data.anesthesiaMethod || '',
      surgeryStarttime: data.surgeryStarttime || null,
      surgeryEndtime: data.surgeryEndtime || null,
      surgeryProcess: data.surgeryProcess || ''
    })

    console.log('手术记录加载成功:', data)

    // 强制初始化文本框高度
    setTimeout(() => {
      forceInitTextareaHeight()
    }, 100)
  } catch (error) {
    console.error('加载手术记录失败:', error)
    ElMessage.error('加载手术记录失败: ' + error.message)
  } finally {
    loading.value = false
  }
}

// 保存手术记录
const handleSave = async () => {
  if (!props.patient?.visitSn) {
    ElMessage.error('缺少患者信息，无法保存')
    return
  }

  try {
    saveLoading.value = true

    // 构建保存数据
    const saveData = {
      visitSn: props.patient.visitSn,
      recordSn: recordData.recordSn,
      ...formData
    }

    console.log('正在保存手术记录:', saveData)

    const result = await apiServices.surgeryRecord.save(saveData)

    // 更新记录信息
    if (result.recordSn) {
      recordData.recordSn = result.recordSn
    }
    if (result.recordDatetime) {
      recordData.recordDatetime = result.recordDatetime
    }
    if (result.recordUpdateDatetime) {
      recordData.recordUpdateDatetime = result.recordUpdateDatetime
    }

    ElMessage.success('保存成功')
    console.log('手术记录保存成功:', result)
  } catch (error) {
    console.error('保存手术记录失败:', error)
    ElMessage.error('保存失败: ' + error.message)
  } finally {
    saveLoading.value = false
  }
}

// 删除手术记录
const handleDelete = async () => {
  if (!props.patient?.visitSn) {
    ElMessage.error('缺少患者信息，无法删除')
    return
  }

  try {
    await ElMessageBox.confirm(
      '确定要删除这条手术记录吗？删除后无法恢复。',
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }
    )

    deleteLoading.value = true

    console.log('正在删除手术记录，visitSn:', props.patient.visitSn)

    await apiServices.surgeryRecord.delete(props.patient.visitSn)

    // 清空表单数据
    Object.assign(formData, {
      preOperativeDiagnosis: '',
      postOperationDiagnosis: '',
      surgeryName: '',
      anesthesiaMethod: '',
      surgeryStarttime: null,
      surgeryEndtime: null,
      surgeryProcess: ''
    })

    // 清空记录数据
    Object.assign(recordData, {
      recordDatetime: '',
      recordUpdateDatetime: '',
      recordSn: '',
      visitSn: props.patient.visitSn
    })

    ElMessage.success('删除成功')
    console.log('手术记录删除成功')
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除手术记录失败:', error)
      ElMessage.error('删除失败: ' + error.message)
    }
  } finally {
    deleteLoading.value = false
  }
}

// 监听患者变化，重新加载数据
watch(() => props.patient?.visitSn, (newVisitSn) => {
  if (newVisitSn) {
    loadSurgeryRecord()
  }
}, { immediate: false })

// 监听手术经过内容变化，自动调整高度
watch(() => formData.surgeryProcess, (newValue, oldValue) => {
  // 只有在值真正改变时才调整高度，避免无限循环
  if (newValue !== oldValue) {
    nextTick(() => {
      nextTick(() => {
        setTimeout(() => {
          adjustSurgeryProcessHeight()
        }, 10) // 短延迟确保DOM更新完成
      })
    })
  }
}, { flush: 'post' })

// 组件挂载时加载数据
onMounted(() => {
  if (props.patient?.visitSn) {
    loadSurgeryRecord()
  } else {
    // 即使没有数据也要初始化文本框高度
    nextTick(() => {
      nextTick(() => {
        adjustSurgeryProcessHeight()
      })
    })
  }
})

// ========== 文本框输入处理方法 ==========

/**
 * 处理手术经过输入变化
 */
const handleSurgeryProcessInput = () => {
  nextTick(() => {
    adjustSurgeryProcessHeight()
  })
}

/**
 * 强制初始化文本框高度（用于数据加载后）
 */
const forceInitTextareaHeight = () => {
  console.log('开始强制初始化文本框高度...')

  // 多次尝试，确保成功
  let attempts = 0
  const maxAttempts = 5

  const tryAdjust = () => {
    attempts++
    console.log(`尝试调整高度，第 ${attempts} 次`)

    if (surgeryProcessRef.value) {
      adjustSurgeryProcessHeight()
      console.log('高度调整完成')
    } else if (attempts < maxAttempts) {
      console.log('文本框引用不存在，100ms后重试...')
      setTimeout(tryAdjust, 100)
    } else {
      console.log('达到最大尝试次数，停止调整')
    }
  }

  tryAdjust()
}

/**
 * 动态调整手术经过文本框高度
 */
const adjustSurgeryProcessHeight = () => {
  if (!surgeryProcessRef.value) {
    console.log('adjustSurgeryProcessHeight: surgeryProcessRef 不存在')
    return
  }

  try {
    const textareaElement = surgeryProcessRef.value.$el.querySelector('.el-textarea__inner')
    if (!textareaElement) {
      console.log('adjustSurgeryProcessHeight: textareaElement 不存在')
      return
    }

    // 确保文本内容已经设置到DOM中
    const currentValue = formData.surgeryProcess || ''
    if (textareaElement.value !== currentValue) {
      textareaElement.value = currentValue
    }

    // 临时设置高度为auto来获取内容的真实高度
    textareaElement.style.height = 'auto'

    // 强制重新计算布局
    textareaElement.offsetHeight

    // 获取内容的滚动高度
    const scrollHeight = textareaElement.scrollHeight

    // 计算最小高度（4行）
    const computedStyle = window.getComputedStyle(textareaElement)
    const fontSize = parseFloat(computedStyle.fontSize) || 14
    const lineHeight = parseFloat(computedStyle.lineHeight) || fontSize * 1.6
    const paddingTop = parseFloat(computedStyle.paddingTop) || 12
    const paddingBottom = parseFloat(computedStyle.paddingBottom) || 12
    const borderTop = parseFloat(computedStyle.borderTopWidth) || 1
    const borderBottom = parseFloat(computedStyle.borderBottomWidth) || 1

    // 4行的最小高度
    const minHeight = lineHeight * 4 + paddingTop + paddingBottom + borderTop + borderBottom

    // 计算最终高度：内容高度和最小高度的较大值，并添加一些缓冲
    const finalHeight = Math.max(scrollHeight + 2, minHeight) // +2px 缓冲

    // 设置新的高度
    textareaElement.style.height = finalHeight + 'px'

    console.log('动态调整高度:', {
      currentValue: currentValue.substring(0, 50) + (currentValue.length > 50 ? '...' : ''),
      textLength: currentValue.length,
      scrollHeight,
      minHeight,
      finalHeight,
      lineHeight,
      fontSize,
      textareaValue: textareaElement.value.substring(0, 50) + (textareaElement.value.length > 50 ? '...' : '')
    })

  } catch (error) {
    console.warn('调整文本框高度时出错:', error)
  }
}

// ========== 诊断信息选择相关方法 ==========

/**
 * 处理术前诊断输入框点击
 */
const handlePreOperativeDiagnosisClick = () => {
  showPreOperativeDiagnosisSelector.value = true
}

/**
 * 处理术后诊断输入框点击
 */
const handlePostOperationDiagnosisClick = () => {
  showPostOperationDiagnosisSelector.value = true
}

/**
 * 处理术前诊断信息确认选择
 */
const handlePreOperativeDiagnosisConfirm = (diagnosisText) => {
  console.log('返回的术前诊断信息:', diagnosisText)

  // 将拼接的诊断信息字符串填入输入框
  if (diagnosisText) {
    formData.preOperativeDiagnosis = diagnosisText
    ElMessage.success('术前诊断信息选择成功')
  }
}

/**
 * 处理术后诊断信息确认选择
 */
const handlePostOperationDiagnosisConfirm = (diagnosisText) => {
  console.log('返回的术后诊断信息:', diagnosisText)

  // 将拼接的诊断信息字符串填入输入框
  if (diagnosisText) {
    formData.postOperationDiagnosis = diagnosisText
    ElMessage.success('术后诊断信息选择成功')
  }
}
</script>

<style scoped>
/* 整体容器 */
.surgery-record-container {
  display: flex;
  width: 100%;
  height: calc(100vh - 180px); /* 使用视口高度减去导航栏高度，确保一屏显示 */
  gap: 20px;
}

/* 右侧记录内容区域 */
.record-content-area {
  flex: 1;
  height: 100%; /* 使用100%高度，与容器保持一致 */
  background: #FFFFFF;
  border-radius: 0 12px 12px 0;
  padding: 16px; /* 减少内边距以适配更多内容 */
  display: flex;
  flex-direction: column;
  overflow-y: auto; /* 添加垂直滚动，以防内容过多 */
}

/* 加载容器 */
.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
  min-height: 400px;
}

/* 页面标题、时间信息和按钮行 */
.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  flex-shrink: 0; /* 防止压缩 */
}

.header-actions {
  display: flex;
  align-items: center;
  gap: 40px;
}

.title-text {
  width: auto; /* 自动宽度 */
  height: 20px;
  font-family: 'PingFang SC', sans-serif;
  font-weight: 500;
  font-size: 18px;
  color: #172B4D;
  line-height: 20px;
  text-align: left;
  font-style: normal;
  text-transform: none;
  margin: 0; /* 移除默认margin */
}

/* 时间信息头部 */
.time-info-section {
  display: flex;
  gap: 40px;
}

.time-item {
  display: flex;
  align-items: center;
  gap: 8px;
}

.time-label {
  width: 80px;
  height: 20px;
  font-family: 'PingFang SC', sans-serif;
  font-weight: 400;
  font-size: 16px;
  color: #6B778C;
  line-height: 20px;
  text-align: left;
}

.time-value {
  font-family: 'PingFang SC', sans-serif;
  font-weight: 400;
  font-size: 16px;
  color: #172B4D;
  line-height: 20px;
}

/* 表单区域 */
.record-form {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 10px; /* 减少表单项之间的间距以节省空间 */
  min-height: 0; /* 允许内容压缩 */
}

.form-item {
  display: flex;
  align-items: flex-start;
  gap: 12px;
}

/* 两列表单行样式 */
.form-item-row {
  display: flex;
  gap: 16px; /* 减少列间距 */
  width: 100%;
}

.form-item-half {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  flex: 1;
  /* 设置最小宽度以确保标签和输入框有足够空间 */
  min-width: calc(130px + 12px + 200px); /* 标签宽度 + 间距 + 最小输入框宽度 */
}

.form-label {
  width: 130px;
  font-family: 'PingFang SC', sans-serif;
  font-weight: 400;
  font-size: 16px;
  color: #172B4D;
  line-height: 20px;
  text-align: right;
  margin-top: 8px;
  flex-shrink: 0;
}

/* 确保两列布局中的标签与单列布局保持一致的对齐 */
.form-item-half .form-label {
  width: 130px; /* 与单列布局保持相同宽度，确保冒号垂直对齐 */
}

/* 响应式布局 - 当空间不足时改为单列 */
@media (max-width: 1200px) {
  .form-item-row {
    flex-direction: column;
    gap: 16px;
  }

  .form-item-half {
    min-width: auto; /* 移除最小宽度限制 */
  }

  .form-item-half .form-label {
    width: 130px; /* 保持标签宽度一致 */
  }
  .time-info-section{
    display: none;
  }
}

/* ======================================================= */
/* ====== 日期时间选择器 (DateTimePicker) 样式 ====== */
/* ======================================================= */

/* 日期时间选择器输入框包装器 - 默认状态 */
:deep(.el-date-editor.form-date-picker .el-input__wrapper) {
  border: 1px solid #EBECF0 !important; /* 与文本框完全一致 */
  box-shadow: 0 0 0 1px #e5e7eb inset;
  background-color: #FFFFFF !important;
  border-radius: 4px !important;
  transition: border-color 0.2s ease !important;
  /* 强制重置所有可能影响边框显示的属性 */
  outline: none !important;
  background-image: none !important;
  filter: none !important;
  opacity: 1 !important;
}

/* 鼠标悬浮状态 - 与文本框保持一致，不改变边框颜色 */
:deep(.el-date-editor.form-date-picker:hover .el-input__wrapper) {
  border-color: #DFE1E6 !important;
  box-shadow: 0 0 0 1px #c0c4cc inset;
}

/* 聚焦状态 - 移除默认的淡蓝色边框，使用自定义样式 */
:deep(.el-date-editor.form-date-picker .el-input__wrapper.is-focus),
:deep(.el-date-editor.form-date-picker.is-focus .el-input__wrapper) {
  border-color: #1678FF !important;

}

/* 输入框内部样式 */
:deep(.el-date-editor.form-date-picker .el-input__inner) {
  font-family: 'PingFang SC', sans-serif !important;
  font-size: 14px !important;
  color: #172B4D !important;
  background-color: transparent !important;
  border: none !important;
  box-shadow: none !important;
  height: auto !important;
  line-height: 1.4 !important;
}

/* 占位符样式 */
:deep(.el-date-editor.form-date-picker .el-input__inner::placeholder) {
  color: #C1C7D0 !important;
  font-family: 'PingFang SC', sans-serif !important;
}

/* 文本框样式 */
.form-textarea {
  width: 100%;
}

.form-textarea.small :deep(.el-textarea__inner) {
  height: 50px; /* 减少高度以节省空间 */
  background: #FFFFFF;
  border-radius: 4px;
  border: 1px solid #EBECF0;
  font-family: 'PingFang SC', sans-serif;
  font-size: 14px;
  color: #172B4D;
  resize: none;
  line-height: 1.3; /* 调整行高 */
}

/* 固定高度的大文本框样式（非自适应） */
.form-textarea.large:not(.autosize) :deep(.el-textarea__inner) {
  height: 80px; /* 减少大文本框高度以适配一屏显示 */
  background: #FFFFFF;
  border-radius: 4px;
  border: 1px solid #EBECF0;
  font-family: 'PingFang SC', sans-serif;
  font-size: 14px;
  color: #172B4D;
  resize: none;
  line-height: 1.3; /* 调整行高 */
}

/* 手术经过自适应高度文本框专用样式 - 使用高优先级选择器 */
.surgery-record-container .record-content-area .form-textarea.autosize-surgery-process :deep(.el-textarea__inner) {
  background: #FFFFFF !important;
  border-radius: 4px !important;
  border: 1px solid #EBECF0 !important;
  font-family: 'PingFang SC', sans-serif !important;
  font-size: 14px !important;
  color: #172B4D !important;
  resize: none !important;
  line-height: 1.6 !important;
  padding: 5px 12px !important;
  box-sizing: border-box !important;
  /* 隐藏滚动条 */
  overflow-y: hidden !important;
  /* 不设置min-height和height，完全由JavaScript动态控制 */
}

/* 手术经过文本框聚焦状态 */
.surgery-record-container .record-content-area .form-textarea.autosize-surgery-process :deep(.el-textarea__inner:focus) {
  border-color: #1678FF !important;
}

/* 文本框聚焦状态 */
.form-textarea :deep(.el-textarea__inner:focus) {
  border-color: #1678FF;
}

/* 自适应文本框聚焦状态 */
.form-textarea.autosize :deep(.el-textarea__inner:focus) {
  border-color: #1678FF !important;
}

/* 手术经过自适应文本框聚焦状态 */
.form-textarea.large.autosize :deep(.el-textarea__inner:focus) {
  border-color: #1678FF !important;
}

/* 文本框占位符样式 */
.form-textarea :deep(.el-textarea__inner::placeholder) {
  color: #C1C7D0;
  font-family: 'PingFang SC', sans-serif;
}

/* 底部按钮区域 */
.form-actions {
  display: flex;
  justify-content: flex-end;
  gap: 16px;
  flex-shrink: 0; /* 防止压缩 */
}

.delete-btn {
  width: 80px;
  height: 36px;
  background: #FFFFFF;
  border: 1px solid #FF5630;
  border-radius: 4px;
  font-family: 'PingFang SC', sans-serif;
  font-weight: 400;
  font-size: 14px;
  color: #FF5630;
  line-height: 20px;
}

.delete-btn:hover {
  background: #FFF2F0;
  border-color: #FF5630;
  color: #FF5630;
}

.save-btn {
  width: 80px;
  height: 36px;
  background: #1678FF;
  border: 1px solid #1678FF;
  border-radius: 4px;
  font-family: 'PingFang SC', sans-serif;
  font-weight: 400;
  font-size: 14px;
  color: #FFFFFF;
  line-height: 20px;
}

.save-btn:hover {
  background: #0052CC;
  border-color: #0052CC;
}

/* 诊断输入框点击样式 */
.diagnosis-clickable :deep(.el-textarea__inner) {
  cursor: pointer;
}

.diagnosis-clickable :deep(.el-textarea__inner:hover) {
  border-color: #1678FF;
}
</style>
